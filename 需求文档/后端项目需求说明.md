# 后端项目需求说明

## 一、技术选型
- JDK 17
- Spring Boot
- Spring Cloud
- Nacos（注册中心与配置中心）
- OpenFeign（服务间调用）
- Sentinel（服务容错与限流）
- Gateway（API网关）
- Seata（分布式事务）
- Mybatis
- Mybatis-Plus
- Lombok
- MySQL 8.0（关系型数据库）
- Druid（数据库连接池/数据源）
- Swagger

## 二、业务模块
1. 订单服务（order-service）
   - 负责订单的创建、查询、管理
   - 需与库存服务、账户服务进行分布式事务处理
2. 库存服务（inventory-service）
   - 管理商品库存，支持库存扣减、回滚
   - 提供库存查询接口
3. 账户服务（account-service）
   - 管理用户账户余额，支持余额扣减、回滚
   - 提供账户查询接口

## 三、系统架构
- 微服务架构，每个业务模块独立部署
- 服务注册与发现通过 Nacos 实现
- 服务间调用采用 OpenFeign
- 分布式事务通过 Seata 管理，保证订单、库存、账户操作的一致性
- 网关统一入口，路由到各业务服务，支持限流与熔断（Sentinel）
- 配置中心统一管理服务配置（Nacos）

## 四、功能流程示例
1. 用户下单：
   - 订单服务发起下单请求
   - 调用库存服务扣减库存
   - 调用账户服务扣减余额
   - Seata 管理分布式事务，确保三方操作一致性
2. 查询订单/库存/账户信息：
   - 各服务提供独立查询接口，通过网关统一访问

## 五、非功能性需求
- 服务高可用，支持自动注册与发现
- 接口限流与熔断，保障系统稳定性
- 配置热更新，便于运维管理
- 日志与监控，便于故障排查

## 六、后续扩展建议
- 可扩展更多业务模块（如支付、物流等）
- 支持多环境配置与灰度发布

## 七、DDD领域驱动设计模块结构树状图

```text
zhao-springcloud
├── zhao-dependencies    # 父 BOM（packaging=pom，仅 dependencyManagement；统一三方依赖版本）
├── zhao-parent          # 聚合父（packaging=pom；声明 modules 与 pluginManagement，导入 BOM）
├── common               # 公共模块（工具类、通用DTO、异常等）
├── order-service
│   ├── application      # 应用服务层
│   ├── domain           # 领域层（聚合、实体、值对象、领域服务、工厂、仓储接口）
│   ├── infrastructure   # 基础设施层（持久化、第三方服务、消息等）
│   └── interfaces       # 接口层（controller、feign、dto、api）
├── inventory-service
│   ├── application
│   ├── domain
│   ├── infrastructure
│   └── interfaces
├── account-service
│   ├── application
│   ├── domain
│   ├── infrastructure
│   └── interfaces
└── gateway              # 网关服务
```


## 九、依赖版本统一约定

- Spring Boot 父 POM 统一：`org.springframework.boot:spring-boot-starter-parent:3.4.8`
- 所有业务模块均通过 `zhao-parent` 继承该版本，禁止各子模块单独覆写 Spring Boot 版本。
- 若使用 `zhao-dependencies` 导入 `spring-boot-dependencies`，其版本同样需保持为 `3.4.8`，以保证依赖对齐。

## 十、规范与约定

### 10.1 MapStruct 做装配
- 目的：解耦 DTO/领域模型/持久化对象之间的转换，提升可读性与一致性。
- 约定：
  - 组件模型：`@Mapper(componentModel = "spring")`
  - 装配类命名：`*Mapper` 或 `*Assembler`，放置在 `application.assembler` 或 `infrastructure.persistence.converter`
  - Lombok 搭配使用 `lombok-mapstruct-binding`（在父 POM 统一 annotationProcessor）
- 示例：
```java
// com.zhao.order.application.assembler.OrderAssembler
@Mapper(componentModel = "spring")
public interface OrderAssembler {
    Order toDomain(CreateOrderCommand command);
    OrderResponse toResponse(Order domain);
}
```

### 10.2 错误码与统一响应结构
- 错误码命名：`{服务简称}-{模块两位}-{三位编号}`，示例：`ORD-01-001`（订单-下单-库存不足）
- 统一响应：
```json
{
  "code": "ORD-01-001",
  "message": "库存不足",
  "traceId": "${traceId}",
  "data": null
}
```
- 控制器异常处理：统一在 `interfaces.web.advice.GlobalExceptionHandler` 兜底并映射错误码。

### 10.3 配置与命名规则（Nacos）
- 命名空间：`dev` / `test` / `prod`
- Group：`DEFAULT_GROUP`（通用）或按业务 `BUSINESS_GROUP`
- DataId：`{service-name}-{profile}.yml`（示例：`order-service-dev.yml`）
- Sentinel 规则持久化：`sentinel-{service}.json` 或 `sentinel-{service}.yml`，与服务配置同命名空间/Group

### 10.4 MyBatis-Plus 规则与 BaseMapper
- 实体与表：
  - 表名小写下划线，实体使用 `@TableName("order")`
  - 公共字段：`create_time`、`update_time` 自动填充；可启用逻辑删除字段 `deleted TINYINT(1)`
- 插件：开启分页、乐观锁、性能分析等所需插件（在 `infrastructure.config.MybatisPlusConfig`）
- BaseMapper 使用：
```java
// com.zhao.order.infrastructure.persistence.mapper.OrderMapper
public interface OrderMapper extends BaseMapper<OrderDO> {}
```
- XML 与注解：鼓励简单 CRUD 走 BaseMapper/Service；复杂查询可配合 XML（放置于 `resources/mapper/*.xml`）。

## 十一、数据库与连接池规范（MySQL 8.0 + Druid）

- 数据库版本与引擎
  - MySQL 8.0（InnoDB 引擎）
  - 字符集：`utf8mb4`，排序规则：`utf8mb4_general_ci`
  - 时间：库内保存 UTC；接口按东八区返回（或前端自适应时区）。

- 命名与建表
  - 表名/字段名统一小写下划线风格；主键 `BIGINT AUTO_INCREMENT`
  - 时间字段：`create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP`、`update_time TIMESTAMP NULL`
  - 金额使用 `DECIMAL(10,2)`；严禁浮点类型存金额
  - 建议使用 Flyway 管理 DDL/变更脚本（`V{版本}__{描述}.sql`）

- 连接池（Druid）推荐参数
  - 依赖：`com.alibaba:druid-spring-boot-starter`
  - 关键参数：
    - `initialSize=5`、`minIdle=5`、`maxActive=50`、`maxWait=60000`
    - `validationQuery=SELECT 1`、`testWhileIdle=true`、`testOnBorrow=false`、`testOnReturn=false`
    - `timeBetweenEvictionRunsMillis=60000`、`minEvictableIdleTimeMillis=300000`
    - 监控：`stat,slf4j`（按需启用）

- Spring Boot 参考配置示例
```yaml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://{host}:{port}/{db}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 50
      max-wait: 60000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: stat,slf4j
```

- 多环境配置与密钥管理
  - 不同环境库名/账号分离；敏感信息通过 Nacos 或 Secret/KMS 管理
  - Nacos DataId 约定：`{service-name}-{profile}.yml`，例如：`order-service-dev.yml`

- 服务与数据库对应关系
  - `order-service` 使用数据库：`order`
  - `inventory-service` 使用数据库：`inventory`
  - `account-service` 使用数据库：`account`

## 十二、容器化部署与启动（Docker + Docker Compose）

- 依赖要求
  - 已安装 Docker（建议 Docker Desktop）与 Docker Compose v2
  - 本地已配置可用的 MySQL 8.0（账号：`root`，密码：`123456`），并已创建数据库 `order`、`inventory`、`account`

- 构建与启动
  1) 打包
  ```bash
  mvn -f zhao-parent/pom.xml -DskipTests clean package
  ```
  2) 启动容器
  ```bash
  docker compose up -d --build
  ```
  3) 查看状态
  ```bash
  docker compose ps
  ```

- 验证接口
  - 网关: `GET http://localhost:8080/api/v1/gateway/ping`
  - 订单: `GET http://localhost:8081/api/v1/order/ping`
  - 库存: `GET http://localhost:8082/api/v1/inventory/ping`
  - 账户: `GET http://localhost:8083/api/v1/account/ping`

- 数据库连接说明（重要）
  - 当前 Compose 未内置 MySQL，微服务容器需连接宿主机 MySQL
  - Mac/Windows（Docker Desktop）：请将 JDBC 地址改用 `host.docker.internal` 访问宿主机
    - 示例：`********************************************?...`
  - Linux：如需从容器访问宿主机，可：
    - 方案 A：在 Compose 使用 host-gateway（Docker 需要 20.10+）
    - 方案 B：改为在 Compose 中增加 MySQL 服务，并将 JDBC 指向该服务名（如 `mysql`）

- 常用运维命令
  ```bash
  # 停止并清理
  docker compose down -v
  # 重新构建与启动
  docker compose up -d --build
  # 查看日志
  docker compose logs -f order-service
  ```

- 服务与数据库对应关系
  - `order-service` 使用数据库：`order`
  - `inventory-service` 使用数据库：`inventory`
  - `account-service` 使用数据库：`account`

### order-service 模块目录与包名约定

```text
order-service
├── src
│   ├── main
│   │   ├── java
│   │   │   └── com
│   │   │       └── zhao
│   │   │           └── order
│   │   │               ├── OrderServiceApplication.java
│   │   │               ├── application                 # 应用层：编排用例、事务脚本、组装 DTO
│   │   │               │   ├── service
│   │   │               │   │   └── OrderAppService.java
│   │   │               │   ├── command                # 命令对象
│   │   │               │   │   └── CreateOrderCommand.java
│   │   │               │   ├── query                  # 查询服务/对象
│   │   │               │   │   └── OrderQueryService.java
│   │   │               │   └── assembler              # DTO 与领域对象互转
│   │   │               │       └── OrderAssembler.java
│   │   │               ├── domain                      # 领域层：实体/值对象/领域服务/工厂/仓储接口
│   │   │               │   ├── model
│   │   │               │   │   └── order
│   │   │               │   │       ├── Order.java
│   │   │               │   │       ├── OrderId.java
│   │   │               │   │       ├── OrderStatus.java
│   │   │               │   │       └── LineItem.java
│   │   │               │   ├── repository
│   │   │               │   │   └── OrderRepository.java
│   │   │               │   ├── service
│   │   │               │   │   └── OrderDomainService.java
│   │   │               │   └── factory
│   │   │               │       └── OrderFactory.java
│   │   │               ├── infrastructure              # 基础设施：持久化、远程调用、配置
│   │   │               │   ├── config
│   │   │               │   │   ├── MybatisPlusConfig.java
│   │   │               │   │   ├── FeignConfig.java
│   │   │               │   │   └── SeataConfig.java
│   │   │               │   ├── persistence
│   │   │               │   │   ├── mapper             # MyBatis / MyBatis-Plus Mapper
│   │   │               │   │   │   └── OrderMapper.java
│   │   │               │   │   ├── po                 # 持久化对象（Data Object）
│   │   │               │   │   │   └── OrderDO.java
│   │   │               │   │   └── converter          # DO 与领域模型转换
│   │   │               │   │       └── OrderPOConverter.java
│   │   │               │   └── remote                 # 远程调用（Feign）
│   │   │               │       ├── inventory
│   │   │               │       │   └── InventoryClient.java
│   │   │               │       └── account
│   │   │               │           └── AccountClient.java
│   │   │               └── interfaces                  # 接口层：Controller/DTO/Feign API 声明
│   │   │                   ├── web
│   │   │                   │   ├── OrderController.java
│   │   │                   │   └── advice
│   │   │                   │       └── GlobalExceptionHandler.java
│   │   │                   ├── dto
│   │   │                   │   ├── CreateOrderRequest.java
│   │   │                   │   └── OrderResponse.java
│   │   │                   └── feign                   # 若暴露给其它服务调用的 API 声明
│   │   │                       └── OrderApi.java
│   │   └── resources
│   │       ├── application.yml
│   │       └── mapper
│   │           └── OrderMapper.xml                    # 使用 XML 时保留；MP 可选
│   └── test
│       └── java
│           └── com
│               └── zhao
│                   └── order
│                       └── OrderServiceApplicationTests.java
└── pom.xml
```

- 包名根：`com.zhao.order`
- 典型包路径示例：
  - 应用服务：`com.zhao.order.application.service`
  - 领域模型：`com.zhao.order.domain.model.order`
  - 仓储接口：`com.zhao.order.domain.repository`
  - 基础设施（持久化）：`com.zhao.order.infrastructure.persistence.mapper|po|converter`
  - 基础设施（远程）：`com.zhao.order.infrastructure.remote.inventory|account`
  - 接口层（Web）：`com.zhao.order.interfaces.web`
  - 接口层（DTO）：`com.zhao.order.interfaces.dto`
  - 接口层（Feign API）：`com.zhao.order.interfaces.feign`

## 八、核心业务表结构设计

```sql
-- 订单表
CREATE TABLE order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    count INT NOT NULL COMMENT '购买数量',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    status VARCHAR(20) NOT NULL COMMENT '订单状态',
    create_time DATETIME NOT NULL COMMENT '创建时间',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间'
);

-- 库存表
CREATE TABLE inventory (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '库存ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    total INT NOT NULL COMMENT '总库存',
    used INT NOT NULL COMMENT '已用库存',
    residue INT NOT NULL COMMENT '剩余库存',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间'
);

-- 账户表
CREATE TABLE account (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '账户ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    balance DECIMAL(10,2) NOT NULL COMMENT '账户余额',
    update_time DATETIME DEFAULT NULL COMMENT '更新时间'
);
```