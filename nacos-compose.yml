version: "3.9"
services:
  nacos:
    image: nacos/nacos-server:v2.5.0
    container_name: nacos-standalone
    environment:
      - MODE=standalone
      - SPRING_SECURITY_IGNORED=true
    ports:
      - "8848:8848"
      - "9848:9848"
    volumes:
      - nacos-data:/home/<USER>/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos"]
      interval: 10s
      timeout: 10s
      retries: 30
      start_period: 60s

volumes:
  nacos-data: