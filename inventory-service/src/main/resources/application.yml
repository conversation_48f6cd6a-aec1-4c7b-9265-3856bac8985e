server:
  port: 8082

spring:
  application:
    name: inventory-service
  cloud:
    nacos:
      discovery:
        server-addr: nacos:8848
        fail-fast: false
        retry:
          times: 5
          interval: 2000
    compatibility-verifier:
      enabled: false
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ****************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 50
      max-wait: 60000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      filters: stat,slf4j

# flyway 已移除

management:
  endpoints:
    web:
      exposure:
        include: health,info