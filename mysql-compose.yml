version: "3.9"
services:
  mysql:
    image: mysql:8.0
    container_name: mysql-8.0
    environment:
      MYSQL_ROOT_PASSWORD: 123
      MYSQL_ROOT_HOST: "%"
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    command:
      - --default-authentication-plugin=mysql_native_password
      - --character-set-server=utf8mb4
      - --collation-server=utf8mb4_unicode_ci
      - --explicit_defaults_for_timestamp=true
      - --bind-address=0.0.0.0
      - --skip-name-resolve
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123"]
      interval: 10s
      timeout: 5s
      retries: 30
      start_period: 60s

volumes:
  mysql-data: