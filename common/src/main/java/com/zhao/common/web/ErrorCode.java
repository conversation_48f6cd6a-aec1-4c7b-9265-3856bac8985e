package com.zhao.common.web;

public enum ErrorCode {
    SUCCESS("SUCCESS", "成功"),
    SERVER_ERROR("SERVER-00-500", "服务器内部错误");

    private final String code;
    private final String defaultMessage;

    ErrorCode(String code, String defaultMessage) {
        this.code = code;
        this.defaultMessage = defaultMessage;
    }

    public String getCode() {
        return code;
    }

    public String getDefaultMessage() {
        return defaultMessage;
    }
}


